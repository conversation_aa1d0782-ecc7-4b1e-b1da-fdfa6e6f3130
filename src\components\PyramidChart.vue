<template>
  <div class="bar-card">
    <div ref="volumn" class="volume" />
  </div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: 'PyramidChart',
  data() {
    return {
      myChart: null,
    };
  },
  mounted() {
    // 获取数据。
    if (this.$refs.volumn) {
      this.reloadChart();
      // 自适应浏览器。
      window.addEventListener("resize", () => {
        this.reloadChart();
      });
    }
  },
  // 组件销毁。
  beforeUnmount() {
    this.disposeChart();
  },
  methods: {
    drawChart() {
      this.myChart = this.$echarts.init(this.$refs.volumn);
      var data = [15, 8, 12, 4, 8, 10, 9];
      var xData = [
        "神经",
        "智力"
      ];
      var colors = [
        {
          type: "linear",
          x: 0,
          y: 0,
          y2: 0,
          x2: 1,
          global: false,
          colorStops: [
            {
              offset: 0,
              color: "#4379d0",
            },
            {
              offset: 0.5,
              color: "#7a8ddd",
            },
            {
              color: "#a1c4ff",
              offset: 0.5,
            },
            {
              offset: 1,
              color: "#bfd1ff",
            },
          ],
        },
        {
          type: "linear",
          x: 0,
          x2: 1,
          y: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "#bb5763",
            },
            {
              offset: 0.5,
              color: "#d0938c",
            },
            {
              offset: 0.5,
              color: "#ffa1ac",
            },
            {
              offset: 1,
              color: "#ffbabe",
            },
          ],
        },
        {
          type: "linear",
          x: 0,
          x2: 1,
          y: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "#4ea95d",
            },
            {
              offset: 0.5,
              color: "#80bc96",
            },
            {
              offset: 0.5,
              color: "#90e6a0",
            },
            {
              offset: 1,
              color: "#a8e5b7",
            },
          ],
        },
        {
          type: "linear",
          x: 0,
          x2: 1,
          y: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "#ab8b52",
            },
            {
              offset: 0.5,
              color: "#bdb280",
            },
            {
              offset: 0.5,
              color: "#e6c690",
            },
            {
              offset: 1,
              color: "#e5d6aa",
            },
          ],
        },
        {
          type: "linear",
          x: 0,
          x2: 1,
          y: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "#4395b3",
            },
            {
              offset: 0.5,
              color: "#769fc2",
            },
            {
              offset: 0.5,
              color: "#86d4f0",
            },
            {
              offset: 1,
              color: "#a4d1ec",
            },
          ],
        },
        {
          type: "linear",
          x: 0,
          x2: 1,
          y: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "#b0ad52",
            },
            {
              offset: 0.5,
              color: "#b7c284",
            },
            {
              offset: 0.5,
              color: "#f0ed98",
            },
            {
              offset: 1,
              color: "#ebefae",
            },
          ],
        },
        {
          type: "linear",
          x: 0,
          x2: 1,
          y: 0,
          y2: 0,
          colorStops: [
            {
              offset: 0,
              color: "#3c46bc",
            },
            {
              offset: 0.5,
              color: "#7b6fc8",
            },
            {
              offset: 0.5,
              color: "#7e88f8",
            },
            {
              offset: 1,
              color: "#a09ef1",
            },
          ],
        },
      ];

      var renderItem = function (params, api) {
        var yValue = api.value(1);
        var start = api.coord([api.value(0), yValue]);
        var size = api.size([api.value(1) - api.value(0), yValue]);
        // var style = api.style();

        // 最右边的点坐标
        var width = size[0] * 0.6;
        var x = start[0] - width / 2;
        var y = start[1];

        var bottomHeight = 48;

        var points = [[x + width / 2, y]];

        // 左边的坐标点
        points.push([x, size[1] + y]);

        points.push([x + width / 2, size[1] + y + bottomHeight]);

        // 右边的坐标点
        points.push([x + width, size[1] + y]);

        var group = {
          type: "group",
          children: [
            {
              // 左半部分
              type: "polygon",
              shape: {
                points: [
                  [x + width / 2, y],          // 顶点
                  [x, size[1] + y],            // 左底角
                  [x + width / 2, size[1] + y + bottomHeight]  // 底中点
                ]
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#00426C" },
                  { offset: 1, color: "#0065B1" }
                ])
              }
            },
            {
              // 右半部分
              type: "polygon",
              shape: {
                points: [
                  [x + width / 2, y],          // 顶点
                  [x + width, size[1] + y],    // 右底角
                  [x + width / 2, size[1] + y + bottomHeight]  // 底中点
                ]
              },
              style: {
                fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: "#005299" },
                  { offset: 1, color: "#90DFE7" }
                ])
              }
            }
          ]
        }

        return group;
      };

      // 绘制图表
      var option = {
        tooltip: {
          backgroundColor: " rgba(9,35,75,0.7)",
          borderColor: "#2187F9",
          borderWidth: 0,
          borderRadius: 8,
          textStyle: {
            color: "#A7EFFF",
            fontSize: this.fontSize(0.4),
            align: "left",
          },
        },
        grid: {
          top: "20%",
          left: "5%",
          bottom: "12%",
          right: "5%",
          containLabel: true,
        },
        xAxis: {
          data: xData,
          offset: 6,
          axisLine: {
            show: true,
            lineStyle: {
              color: "rgba(127, 208, 255, 0.2)",
              width: 0,
            },
          },
          splitLine: {
            show: false,
            lineStyle: {
              color: "rgba(127, 208, 255, 0.2)",
            },
          },
          axisLabel: {
            show: true,
            color: "#fff",
            fontSize: this.fontSize(0.35),
            interval: 0,
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: [
          {
            inverse: false,
            name: "单位：人",
            nameTextStyle: {
              color: "#fff",
              fontSize: this.fontSize(0.35),
              nameLocation: "start",
            },
            type: "value",
            position: "left",
            axisLabel: {
              show: false,
              fontSize: this.fontSize(0.35),
              color: "#fff",
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "rgba(127, 208, 255, 0.2)",
                width: 1,
              },
            },
            splitLine: {
              show: false,
              lineStyle: {
                color: "rgba(127, 208, 255, 0.2)",
              },
            },
            axisTick: {
              show: false,
            },
          },
        ],

        series: [
          {
            type: "custom",
            itemStyle: {
              color: function (params) {
                return colors[params.dataIndex];
              },
            },

            label: {
              show: true,
              position: "top",
              color: "#ffffff",
              fontSize: this.fontSize(0.35),
              formatter: function (params) {
                return "{a" + params.dataIndex + "|" + params.data + "人}";
              },
              rich: {
                a0: {
                  color: "#77A9FF",
                  fontSize: this.fontSize(0.35),
                  align: "center",
                  fontFamily: "PangMenZhengDao",
                },
                a1: {
                  color: "#FEBBBD",
                  fontSize: this.fontSize(0.35),
                  align: "center",
                  fontFamily: "PangMenZhengDao",
                },
                a2: {
                  color: "#A1EEB3",
                  fontSize: this.fontSize(0.35),
                  align: "center",
                  fontFamily: "PangMenZhengDao",
                },
                a3: {
                  color: "#F3BD5B",
                  fontSize: this.fontSize(0.35),
                  align: "center",
                  fontFamily: "PangMenZhengDao",
                },
                a4: {
                  color: "#57D1FF",
                  fontSize: this.fontSize(0.35),
                  align: "center",
                  fontFamily: "PangMenZhengDao",
                },
                a5: {
                  color: "#F0ED97",
                  fontSize: this.fontSize(0.35),
                  align: "center",
                  fontFamily: "PangMenZhengDao",
                },
                a6: {
                  color: "#9EA0FF",
                  fontSize: this.fontSize(0.35),
                  align: "center",
                  fontFamily: "PangMenZhengDao",
                },
              },
            },
            data: data,
            renderItem: renderItem,
          },
        ],
      };
      this.myChart.setOption(option);
    },
    // 字体自适应。
    fontSize(res) {
      const clientWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth;
      if (!clientWidth) return;
      const fontSize = 40 * (clientWidth / 1920);
      return res * fontSize;
    },
    // 销毁图表。
    disposeChart() {
      if (this.myChart) {
        this.myChart.dispose();
      }
    },
    // 重新加载图表。
    reloadChart() {
      this.disposeChart();
      this.drawChart();
    },
  },
};
</script>

<style scoped>
.bar-card {
  width: 100%;
  height: 100%;

  .volume {
    width: 100%;
    height: 100%;
  }
}
</style>
